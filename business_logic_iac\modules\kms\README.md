# Customer KMS Module

This Terraform module creates customer-specific KMS keys for encrypting resources in the Smart Analytics infrastructure. Each customer gets their own dedicated KMS key for data isolation.

## Features

- **Customer-specific KMS keys** for enhanced security and data isolation
- **Automatic key rotation** enabled
- **Service-specific permissions** for Lambda and S3
- **Cross-service encryption** support
- **Proper key policies** following AWS best practices

## Usage

```hcl
module "customer_kms" {
  source = "../../../../modules/kms/"

  aws_region = "us-east-1"
  environment = "dev"
  country = "us"
  customers = ["washingtoncounty-mn", "flagler", "pvgt"]

  tags = {
    Environment = "dev"
    Project     = "smartanalytics"
  }
}
```

## Key Policy

The KMS key policy allows:

### Root Account Access
- Full administrative access to the AWS account root user
- Required for key management operations

### Lambda Service Access
- **Decrypt**: Lambda can decrypt objects encrypted with this key
- **DescribeKey**: Lambda can get key metadata
- **Condition**: Only via Lambda service in the same region

### S3 Service Access  
- **Encrypt/Decrypt**: S3 can encrypt/decrypt objects
- **GenerateDataKey**: S3 can generate data keys for envelope encryption
- **ReEncrypt**: S3 can re-encrypt objects during key rotation
- **Condition**: Only via S3 service in the same region

## Security Features

- **Key rotation**: Automatically rotates key material annually
- **Deletion protection**: 7-day deletion window for recovery
- **Service isolation**: Keys can only be used by intended services
- **Regional restriction**: Keys can only be used in the deployment region

## Variables

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| aws_region | AWS Region | `string` | n/a | yes |
| environment | Environment (dev, qa, prod) | `string` | n/a | yes |
| country | Country code (us, ca) | `string` | n/a | yes |
| customers | Set of customer names | `set(string)` | n/a | yes |
| tags | Resource tags | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| kms_key_arns | Map of customer names to KMS key ARNs |
| kms_key_ids | Map of customer names to KMS key IDs |
| kms_key_aliases | Map of customer names to KMS key aliases |

## Key Alias

Each customer key is assigned an alias following the naming convention:
```
alias/{environment}-{country}-{customer}-smartanalytics-key
```

Examples:
- `alias/dev-us-flagler-smartanalytics-key`

## Usage by Other Services

### S3 Bucket Encryption
```hcl
resource "aws_s3_bucket_server_side_encryption_configuration" "example" {
  bucket = aws_s3_bucket.example.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = var.customer_kms_keys["customer_name"]
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}
```

### Lambda Function Encryption
```hcl
resource "aws_lambda_function" "example" {
  # ... other configuration
  kms_key_arn = var.customer_kms_keys["customer_name"]
}
```

## Cost Considerations

- **Key usage**: Charged per API request to KMS
- **Bucket key**: Enable S3 bucket key to reduce KMS costs
- **Key rotation**: No additional charges for automatic rotation
- **Cross-region**: Keys cannot be used across regions

## Best Practices

1. **Use bucket keys**: Enable S3 bucket key for cost optimization
2. **Least privilege**: Grant minimum required permissions
3. **Monitor usage**: Set up CloudTrail logging for key usage
4. **Regular review**: Periodically review key policies and usage
5. **Backup strategy**: Document key ARNs for disaster recovery

## Dependencies

- Must be deployed before any modules that require encryption
- No dependencies on other modules

## Deployment Order

1. Deploy `kms` module first
2. Deploy other modules that depend on the KMS key

## Troubleshooting

### Access Denied Errors
1. Verify the service has permission in the key policy
2. Check that the service is using the key in the correct region
3. Ensure the key is not disabled or pending deletion

### High KMS Costs
1. Enable S3 bucket key for S3 encryption
2. Review CloudTrail logs for excessive API calls
3. Consider using AWS managed keys for non-sensitive data

## Security Considerations

- **Key material**: AWS generates and manages the key material
- **Access logging**: All key usage is logged in CloudTrail
- **Compliance**: Meets most compliance requirements for encryption
- **Data residency**: Key material never leaves the AWS region
