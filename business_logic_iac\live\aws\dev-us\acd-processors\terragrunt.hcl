dependency "kms" {
  config_path = "../kms"
}

dependency "s3" {
  config_path = "../s3"
}

inputs = {
  aws_region = "us-east-1"
  environment = "dev"
  country = "us"

  # Customer-specific infrastructure
  customer_s3_buckets = dependency.s3.outputs.s3_bucket_names
  customer_kms_keys = dependency.kms.outputs.kms_key_arns
  redshift_secret_arn = "arn:aws:secretsmanager:us-east-1:123456789012:secret:comtech-analytics/dev/us/redshift_credentials-AbCdEf"
  
  # Customer configurations
  customer_config = {
    "washingtoncounty-mn" = {
      sqs_queue_arn     = "arn:aws:sqs:us-east-1:123456789012:dev-us-washingtoncounty-mn-agent-events"
      sqs_dlq_arn       = "arn:aws:sqs:us-east-1:123456789012:dev-us-washingtoncounty-mn-agent-events-dlq"
      s3_data_lake_arn  = "arn:aws:s3:::dev-washingtoncounty-mn-datalake-raw-01"
      redshift_database = "washingtoncounty-mn_db"
      tenant_key        = "wamn"
      timezone_name     = "America/New_York"
      enable_alarms     = true
      alarm_endpoints = {
        email_addresses = []
        slack_webhooks  = []
      }
    }
    "flagler" = {
      sqs_queue_arn     = "arn:aws:sqs:us-east-1:123456789012:dev-us-flagler-agent-events"
      sqs_dlq_arn       = "arn:aws:sqs:us-east-1:123456789012:dev-us-flagler-agent-events-dlq"
      s3_data_lake_arn  = "arn:aws:s3:::dev-flagler-datalake-raw-01"
      redshift_database = "flagler_db"
      tenant_key        = "flfl"
      timezone_name     = "America/New_York"
      enable_alarms     = false
    }
    "pvgt" = {
      sqs_queue_arn     = "arn:aws:sqs:us-east-1:123456789012:dev-us-pvgt-agent-events"
      sqs_dlq_arn       = "arn:aws:sqs:us-east-1:123456789012:dev-us-pvgt-agent-events-dlq"
      s3_data_lake_arn  = "arn:aws:s3:::dev-pvgt-datalake-raw-01"
      redshift_database = "pvgt_db"
      tenant_key        = "pvgt"
      timezone_name     = "America/New_York"
      enable_alarms     = true
      alarm_endpoints = {
        email_addresses = []
        slack_webhooks  = []
      }
    }
  }
  
  # Lambda configuration
  lambda_runtime = "python3.12"
  lambda_memory_size = 512
  lambda_timeout = 30
  lambda_code_s3_key = "acd-processor.zip"
  
  # SQS configuration
  sqs_batch_size = 10
  sqs_max_batching_window_seconds = 2
  
  # Logging
  log_level = "INFO"
  
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "dev"
      Module      = "acd-processors"
    }
  )
}

terraform {
  source = "../../../../modules/acd-processor"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
