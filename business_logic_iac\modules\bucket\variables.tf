variable "aws_region" {
  description = "AWS Region"
  type        = string
}

variable "environment" {
  description = "Environment (dev, qa, prod)"
  type        = string
}

variable "country" {
  description = "Country code (us, ca)"
  type        = string
  validation {
    condition     = contains(["ca", "us"], var.country)
    error_message = "Valid value is one of the following: ca OR us."
  }
}

variable "customer_config" {
  description = "Set of customer names to create S3 buckets for"
  type        = map(object({
    enabled = bool
    kms_key_arn = string
    customer_name = string
  }))
}

variable "bucket_name" {
  description = "Name of the S3 bucket"
  type        = string
}

variable "s3_lifecycle_noncurrent_version_expiration_days" {
  description = "Number of days after which non-current versions expire"
  type        = number
  default     = 30
  validation {
    condition     = var.s3_lifecycle_noncurrent_version_expiration_days >= 1
    error_message = "Must be at least 1 day"
  }
}

variable "tags" {
  type        = map(string)
  description = "Map of key/value pairs to apply as tags to all resources"
  default     = {}
}
