inputs = {
  aws_region = "us-east-1"
  environment = "prod"
  country = "us"
  customers = ["washingtoncounty-mn", "flagler", "pvgt"]
  
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "prod"
      Module      = "kms"
    }
  )
}

terraform {
  source = "../../../../modules/kms/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
