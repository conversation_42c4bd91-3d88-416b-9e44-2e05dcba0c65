dependency "kms" {
  config_path = "../kms"
}

inputs = {
  aws_region = "us-east-1"
  environment = "dev"
  country = "us"
  customers = ["washingtoncounty-mn", "flagler", "pvgt"]
  
  customer_kms_keys = dependency.kms.outputs.kms_key_arns
  s3_lifecycle_noncurrent_version_expiration_days = 30
  
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "dev"
      Module      = "s3"
    }
  )
}

terraform {
  source = "../../../../modules/s3/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
