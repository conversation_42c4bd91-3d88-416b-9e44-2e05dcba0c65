inputs = {
  aws_region = "ca-central-1"
  environment = "prod"
  country = "ca"
  customers = ["washingtoncounty-mn", "flagler", "pvgt"]
  
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "ca"
      environment = "prod"
      Module      = "kms"
    }
  )
}

terraform {
  source = "../../../../modules/kms/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
