# Customer S3 Module

This Terraform module creates customer-specific S3 buckets for Lambda deployment packages. Each customer gets their own dedicated bucket for Lambda code isolation.

## Features

- **Customer-specific S3 buckets** for Lambda code isolation
- **KMS encryption** using customer-managed key
- **Versioning enabled** for deployment package history
- **Lifecycle policies** to manage storage costs
- **Public access blocked** for security
- **Bucket key enabled** for cost optimization

## Usage

```hcl
module "customer_s3" {
  source = "../../../../modules/shared-s3/"

  aws_region = "us-east-1"
  environment = "dev"
  country = "us"
  customers =   ["washingtoncounty-mn", "flagler", "pvgt"]
  customer_kms_keys = {
    "washingtoncounty-mn" = "arn:aws:kms:us-east-1:123456789012:key/washingtoncounty-mn-key-id"
    "flagler" = "arn:aws:kms:us-east-1:123456789012:key/flagler-key-id"
    "pvgt" = "arn:aws:kms:us-east-1:123456789012:key/pvgt-key-id"
  }

  tags = {
    Environment = "dev"
    Project     = "smartanalytics"
  }
}
```

## Bucket Structure

Each customer gets their own S3 bucket for Lambda deployment packages:
```
dev-us-broward-smartanalytics-lambdas/
├── acd-processor.zip
├── data-transformer.zip
└── ...

dev-us-flagler-smartanalytics-lambdas/
├── acd-processor.zip
├── data-transformer.zip
└── ...
```

## Security

- **Public access blocked**: All public access is denied
- **KMS encryption**: All objects encrypted with customer-managed key
- **Bucket key enabled**: Reduces KMS costs for high-volume operations
- **Versioning**: Maintains history of deployment packages

## Lifecycle Management

- **Non-current versions**: Automatically deleted after 30 days (configurable)
- **Current versions**: Retained indefinitely
- **Incomplete multipart uploads**: Cleaned up automatically

## Variables

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| aws_region | AWS Region | `string` | n/a | yes |
| environment | Environment (dev, qa, prod) | `string` | n/a | yes |
| country | Country code (us, ca) | `string` | n/a | yes |
| customers | Set of customer names | `set(string)` | n/a | yes |
| customer_kms_keys | Map of customer names to KMS key ARNs | `map(string)` | n/a | yes |
| s3_lifecycle_noncurrent_version_expiration_days | Days to retain non-current versions | `number` | `30` | no |
| tags | Resource tags | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| s3_bucket_names | Map of customer names to S3 bucket names |
| s3_bucket_arns | Map of customer names to S3 bucket ARNs |
| s3_bucket_ids | Map of customer names to S3 bucket IDs |

## Dependencies

- Requires KMS keys from `kms` module
- Must be deployed before `acd-processor` module

## Deployment Order

1. Deploy `kms` module
2. Deploy `s3` module (depends on KMS)
3. Deploy `acd-processor` module (depends on S3)

## Cost Optimization

- **Bucket key enabled**: Reduces KMS request costs
- **Lifecycle policies**: Automatically removes old versions
- **Intelligent tiering**: Can be added for further cost savings

## Best Practices

1. **Use consistent naming**: Follow the established naming convention
2. **Tag appropriately**: Include environment, project, and cost center tags
3. **Monitor costs**: Set up billing alerts for S3 usage
4. **Regular cleanup**: Review and clean up unused deployment packages
5. **Access logging**: Consider enabling access logging for audit trails
